import { createApp } from 'vue';
import { createProvider, NodeEnv, ContextMode, Access } from '@vtj/renderer';
import {
  LcdpService,
  STORAGE_KEY,
  ACCESS_PRIVATE_KEY,
  AUTH_PATH,
  MATERIAL_PATH,
  setGlobalRequest,
  appGuard
} from '@/shared';
import { notify, loading, alert } from './adapter';
import App from './App.vue';
import router from './router';
import '@/style/index.scss';

const app = createApp(App);
const service = new LcdpService(notify);
const request = setGlobalRequest({ notify, loading });
const id = location.hash.split('/')[1];
if (id) {
  const access = new Access({
    alert,
    storageKey: STORAGE_KEY,
    privateKey: ACCESS_PRIVATE_KEY,
    auth: AUTH_PATH
  });
  access.connect({ request, router, mode: ContextMode.Runtime });

  const { provider, onReady } = createProvider({
    nodeEnv: NodeEnv.Production,
    mode: ContextMode.Runtime,
    service,
    project: {
      id
    },
    materialPath: MATERIAL_PATH,
    dependencies: {
      Vue: () => import('vue'),
      VueRouter: () => import('vue-router')
    },
    adapter: {
      request
    }
  });

  onReady(async () => {
    app.use(access);
    app.use(router);
    app.use(provider);
    if (await appGuard(id, access)) {
      app.mount('#app');
    }
  });
} else {
  app.use(router);
  app.mount('#app');
}
