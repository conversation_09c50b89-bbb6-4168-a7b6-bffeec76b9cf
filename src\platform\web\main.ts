import { createApp } from 'vue';
import { createProvider, NodeEnv, ContextMode, Access } from '@vtj/renderer';
import {
  LcdpService,
  STORAGE_KEY,
  ACCESS_PRIVATE_KEY,
  AUTH_PATH,
  MATERIAL_PATH,
  setGlobalRequest,
  appGuard
} from '@/shared';
import { notify, loading, alert } from './adapter';
import App from './App.vue';
import router from './router';
import '@/style/index.scss';
const app = createApp(App);
const service = new LcdpService(notify);
const request = setGlobalRequest({ notify, loading });
const id = location.hash.split('/')[1];

// 为页面 DSL 提供全局变量和方法，解决未定义变量的问题
(window as any).currentUser = {
  id: 1,
  name: '演示用户',
  email: '<EMAIL>',
  avatar: '👤',
  role: 'admin'
};

(window as any).shortcuts = [
  { name: '首页', url: '/', icon: '🏠' },
  { name: '设置', url: '/settings', icon: '⚙️' },
  { name: '帮助', url: '/help', icon: '❓' }
];

(window as any).prescriptionStats = {
  total: 0,
  pending: 0,
  completed: 0,
  cancelled: 0
};

(window as any).handleDropdownCommand = (command: string) => {
  console.log('下拉菜单命令:', command);
  // 这里可以根据命令执行相应的操作
  switch (command) {
    case 'profile':
      console.log('查看个人资料');
      break;
    case 'settings':
      console.log('打开设置');
      break;
    case 'logout':
      console.log('退出登录');
      break;
    default:
      console.log('未知命令:', command);
  }
};

if (id) {
  const access = new Access({
    alert,
    storageKey: STORAGE_KEY,
    privateKey: ACCESS_PRIVATE_KEY,
    auth: AUTH_PATH
  });
  access.connect({ request, router, mode: ContextMode.Runtime });
  const { provider, onReady } = createProvider({
    nodeEnv: NodeEnv.Production,
    mode: ContextMode.Runtime,
    service,
    project: {
      id
    },
    materialPath: MATERIAL_PATH,
    dependencies: {
      Vue: () => import('vue'),
      VueRouter: () => import('vue-router')
    },
    adapter: {
      request
    }
  });

  onReady(async () => {
    app.use(router);
    app.use(access);
    app.use(provider);
    if (await appGuard(id, access)) {
      app.mount('#app');
    }
  });
} else {
  app.use(router);
  app.mount('#app');
}
